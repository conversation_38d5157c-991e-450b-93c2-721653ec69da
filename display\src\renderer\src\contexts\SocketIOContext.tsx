import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { redirect, useNavigate } from "@tanstack/react-router"; // Import useNavigate
import { io, type Socket } from "socket.io-client";

import { useToken } from "../stores/auth.store";

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  isServerDown: boolean;
  currentGamePhase: string | null;
  phaseStartTime: number;
  bettingClosedDetected: boolean;
  detectPhaseTransition: (phase: string, countdown: number) => void;
  reconnectSocket: () => void; // Method to manually trigger reconnection
  isInitialLoad: boolean;
}

const SocketIOContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  isServerDown: false,
  currentGamePhase: null,
  phaseStartTime: 0,
  bettingClosedDetected: false,
  detectPhaseTransition: () => {},
  reconnectSocket: () => {}, // Default empty implementation
  isInitialLoad: true, // Initialize as true
});

export const useSocketIO = () => useContext(SocketIOContext);

export const SocketIOProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const token = useToken();

  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isServerDown, setIsServerDown] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true); // Track initial load
  // Track last server activity time for logging
  const [lastServerActivity, setLastServerActivity] = useState(Date.now());

  // Track game phase and transitions for server status detection
  const [currentGamePhase, setCurrentGamePhase] = useState<string>();
  const [phaseStartTime, setPhaseStartTime] = useState<number>(0);
  const [bettingClosedDetected, setBettingClosedDetected] = useState(false);
  const [countdownReachedZero, setCountdownReachedZero] = useState(false);

  // Phase-specific timeouts
  const BETTING_CLOSED_TRANSITION_TIMEOUT = 500; // 500ms after countdown reaches 0

  useEffect(() => {
    if (!token) {
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }

    const backendUrl = "ws://api.playbetman1.com";

    const socketInstance = io(backendUrl, {
      transports: ["websocket"],
      reconnection: true,
      autoConnect: true,
      auth: (cb) => {
        cb({ token });
      },
    });

    socketInstance.on("error", (error) => {
      socketInstance.emit("getEventDetails");
      setIsConnected(false);
      setIsServerDown(true);
      console.log("herereaf 1");
    });

    socketInstance.on("connect_error", (error) => {
      console.log("herereaf 2");
      socketInstance.emit("getEventDetails");
      setIsConnected(false);
      setIsServerDown(true);
    });

    socketInstance.on("connect", () => {
      socketInstance.emit("getEventDetails");
      setIsConnected(true);
      setIsServerDown(false);
      setLastServerActivity(Date.now());
      setIsInitialLoad(false); // Connection successful, no longer initial load
    });

    // Handle reconnection attempts
    socketInstance.on("reconnect_attempt", (attempt) => {
      console.log("herereaf 3");
      socketInstance.emit("getEventDetails");
    });

    // Handle successful reconnection
    socketInstance.on("reconnect", () => {
      socketInstance.emit("getEventDetails");
      setIsConnected(true);
      setLastServerActivity(Date.now());
    });

    // Handle reconnect error
    socketInstance.on("reconnect_error", (error) => {
      setIsServerDown(true);
    });

    // Handle reconnect failed
    socketInstance.on("reconnect_failed", () => {
      setIsServerDown(true);
    });

    socketInstance.on("disconnect", (reason) => {
      setIsConnected(false);
      setIsServerDown(true);
    });

    // Handle game data events
    const handleGameData = (_data: any) => {
      // If we receive any game data, the server is up
      const wasServerDown = isServerDown;
      setIsServerDown(false);
      setLastServerActivity(Date.now());
      console.log("Game data received from server - server is up");

      // Reset countdown zero detection if we receive data
      setCountdownReachedZero(false);

      // Log reconnection success if we were previously marked as down
      if (wasServerDown) {
        console.log("Successfully reconnected to server and receiving data");
      }
    };

    // Set up event listeners for game events
    socketInstance.on("gameStatus", (data) => {
      handleGameData(data);
      console.log("Game status received:", data);
    });

    socketInstance.on("drawnNumbers", (data) => {
      handleGameData(data);
      console.log("Drawn numbers received:", data);
    });

    socketInstance.on("gameHistory", (data) => {
      handleGameData(data);
      console.log("Game history received:", data);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.off("connect");
      socketInstance.off("disconnect");
      socketInstance.off("error");
      socketInstance.off("gameStatus");
      socketInstance.off("drawnNumbers");
      socketInstance.off("gameHistory");

      // Remove reconnection event listeners
      socketInstance.off("reconnect_attempt");
      socketInstance.off("reconnect");
      socketInstance.off("reconnect_error");
      socketInstance.off("reconnect_failed");

      socketInstance.disconnect();
      setSocket((prev) => (prev === socketInstance ? null : prev));
      setIsConnected(false);
    };
  }, [token]);

  useEffect(() => {
    if (isInitialLoad && isServerDown && !isConnected) {
      // Only redirect on initial load if server is down and not connected
      console.log(
        "Initial load network error, redirecting to /network-reconnect",
      );
      redirect({ to: "/network-reconnect", replace: true });
    }
    // No dependency on navigate needed here as it's stable from TanStack Router
  }, [isInitialLoad, isServerDown, isConnected]);

  // Function to detect phase transitions and check for missing data
  const detectPhaseTransition = useCallback(
    (phase: string, countdown: number) => {
      const now = Date.now();

      // If phase has changed, update phase and start time
      if (phase !== currentGamePhase) {
        console.log(`Game phase changed: ${currentGamePhase} -> ${phase}`);
        setCurrentGamePhase(phase);
        setPhaseStartTime(now);

        // Reset detection flags when entering a new phase
        if (phase !== "BETTING_CLOSED") {
          setBettingClosedDetected(false);
        }

        // If we've received BETTING_CLOSED data, reset the countdown zero flag
        if (phase === "BETTING_CLOSED") {
          setCountdownReachedZero(false);
          setBettingClosedDetected(true);
          console.log("BETTING_CLOSED phase detected from server");
        }
      }

      // Phase-specific detection logic - focus exclusively on the countdown reaching zero
      if (
        phase === "BETTING_OPEN" &&
        countdown === 0 &&
        !countdownReachedZero
      ) {
        // Countdown just reached zero, should transition to BETTING_CLOSED immediately
        console.log(
          "CRITICAL: Countdown reached zero, expecting BETTING_CLOSED transition",
        );

        // Mark that we've detected the countdown reaching zero
        setCountdownReachedZero(true);

        // Check if we receive BETTING_CLOSED data within exactly 500ms
        // If we're still in BETTING_OPEN phase after 500ms, the server is down
        if (currentGamePhase === "BETTING_OPEN" && countdownReachedZero) {
          console.log(
            `CRITICAL: No BETTING_CLOSED data received within ${BETTING_CLOSED_TRANSITION_TIMEOUT}ms after countdown reached zero`,
          );

          // Mark the server as down
          setIsServerDown(true);

          // Since the server is down, we need to simulate the BETTING_CLOSED phase
          setCurrentGamePhase("BETTING_CLOSED");
          setBettingClosedDetected(true);

          console.log("Simulating BETTING_CLOSED phase for exactly 5 seconds");

          // After exactly 5 seconds, we should transition back to BETTING_OPEN
          // This will be handled by the simplified offline mode in the main component
        }
        setTimeout(() => {
          setCurrentGamePhase("BETTING_OPEN");
          setPhaseStartTime(now);
        }, BETTING_CLOSED_TRANSITION_TIMEOUT);
      }
    },
    [
      currentGamePhase,
      bettingClosedDetected,
      countdownReachedZero,
      BETTING_CLOSED_TRANSITION_TIMEOUT,
    ],
  );

  // Simple connection status monitoring
  useEffect(() => {
    if (isConnected && isServerDown) {
      // Reset server down status when connected
      setIsServerDown(false);
    }
  }, [isConnected, isServerDown]);

  // Function to manually reconnect the socket
  const reconnectSocket = useCallback(() => {
    console.log("Manual reconnection requested");

    if (socket) {
      console.log("Attempting to manually reconnect socket");

      // Disconnect and reconnect
      if (socket.connected) {
        socket.disconnect();
      }
      socket.connect();
    } else {
      console.log("Cannot reconnect - no socket instance available");
    }
  }, [socket]);

  return (
    <SocketIOContext.Provider
      value={{
        socket,
        isConnected,
        isServerDown,
        currentGamePhase,
        phaseStartTime,
        bettingClosedDetected,
        detectPhaseTransition,
        reconnectSocket,
        isInitialLoad,
      }}
    >
      {children}
    </SocketIOContext.Provider>
  );
};
