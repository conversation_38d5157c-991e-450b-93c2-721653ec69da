{"name": "@tanstack/router-plugin", "version": "1.117.2", "description": "Modern and scalable routing for React applications", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/router.git", "directory": "packages/router-plugin"}, "homepage": "https://tanstack.com/router", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "location", "router", "routing", "async", "async router", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./vite": {"import": {"types": "./dist/esm/vite.d.ts", "default": "./dist/esm/vite.js"}, "require": {"types": "./dist/cjs/vite.d.cts", "default": "./dist/cjs/vite.cjs"}}, "./rspack": {"import": {"types": "./dist/esm/rspack.d.ts", "default": "./dist/esm/rspack.js"}, "require": {"types": "./dist/cjs/rspack.d.cts", "default": "./dist/cjs/rspack.cjs"}}, "./webpack": {"import": {"types": "./dist/esm/webpack.d.ts", "default": "./dist/esm/webpack.js"}, "require": {"types": "./dist/cjs/webpack.d.cts", "default": "./dist/cjs/webpack.cjs"}}, "./esbuild": {"import": {"types": "./dist/esm/esbuild.d.ts", "default": "./dist/esm/esbuild.js"}, "require": {"types": "./dist/cjs/esbuild.d.cts", "default": "./dist/cjs/esbuild.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "engines": {"node": ">=12"}, "dependencies": {"@babel/core": "^7.26.8", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9", "@babel/template": "^7.26.8", "@babel/traverse": "^7.26.8", "@babel/types": "^7.26.8", "@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "babel-dead-code-elimination": "^1.0.10", "chokidar": "^3.6.0", "unplugin": "^2.1.2", "zod": "^3.24.2", "@tanstack/router-core": "^1.117.1", "@tanstack/router-generator": "^1.117.1", "@tanstack/router-utils": "^1.115.0", "@tanstack/virtual-file-routes": "^1.115.0"}, "peerDependencies": {"@rsbuild/core": ">=1.0.2", "vite": ">=5.0.0 || >=6.0.0", "vite-plugin-solid": "^2.11.2", "webpack": ">=5.92.0", "@tanstack/react-router": "^1.117.1"}, "peerDependenciesMeta": {"@rsbuild/core": {"optional": true}, "@tanstack/react-router": {"optional": true}, "vite": {"optional": true}, "vite-plugin-solid": {"optional": true}, "webpack": {"optional": true}}, "scripts": {}}