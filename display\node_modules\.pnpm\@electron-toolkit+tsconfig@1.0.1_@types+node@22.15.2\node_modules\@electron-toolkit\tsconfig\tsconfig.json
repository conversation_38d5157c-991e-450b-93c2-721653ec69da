{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "esnext", "module": "esnext", "sourceMap": false, "strict": true, "jsx": "preserve", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": false, "noImplicitReturns": true}}