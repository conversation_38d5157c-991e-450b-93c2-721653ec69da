{"name": "@tanstack/virtual-file-routes", "version": "1.115.0", "description": "Modern and scalable routing for React applications", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/router.git", "directory": "packages/virtual-file-routes"}, "homepage": "https://tanstack.com/router", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "location", "router", "routing", "async", "async router", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "engines": {"node": ">=12"}, "scripts": {}}