{"name": "@tanstack/react-router", "version": "1.117.1", "description": "Modern and scalable routing for React applications", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/router.git", "directory": "packages/react-router"}, "homepage": "https://tanstack.com/router", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "location", "router", "routing", "async", "async router", "typescript"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "engines": {"node": ">=12"}, "dependencies": {"@tanstack/react-store": "^0.7.0", "jsesc": "^3.1.0", "tiny-invariant": "^1.3.3", "tiny-warning": "^1.0.3", "@tanstack/history": "1.115.0", "@tanstack/router-core": "1.117.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jsesc": "^3.0.3", "@vitejs/plugin-react": "^4.3.4", "combinate": "^1.1.11", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.24.2"}, "peerDependencies": {"react": ">=18.0.0 || >=19.0.0", "react-dom": ">=18.0.0 || >=19.0.0"}, "scripts": {}}