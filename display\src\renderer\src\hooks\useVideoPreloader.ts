import { useState, useEffect, useCallback } from 'react';

export interface VideoPreloadStatus {
  loaded: number;
  total: number;
  isComplete: boolean;
  errors: string[];
  progress: number; // 0-100
}

export interface VideoPreloadOptions {
  priority?: 'high' | 'normal';
  retryAttempts?: number;
  retryDelay?: number;
  onProgress?: (status: VideoPreloadStatus) => void;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export interface UseVideoPreloaderReturn {
  status: VideoPreloadStatus;
  preloadVideos: (urls: string[], options?: VideoPreloadOptions) => Promise<void>;
  isVideoReady: (url: string) => boolean;
  getPreloadedVideo: (url: string) => HTMLVideoElement | null;
  clearCache: () => void;
}

/**
 * Advanced video preloader hook with intelligent caching and retry logic
 */
export function useVideoPreloader(): UseVideoPreloaderReturn {
  const [status, setStatus] = useState<VideoPreloadStatus>({
    loaded: 0,
    total: 0,
    isComplete: false,
    errors: [],
    progress: 0,
  });

  // Cache for preloaded videos
  const [videoCache] = useState(new Map<string, HTMLVideoElement>());
  const [loadingPromises] = useState(new Map<string, Promise<HTMLVideoElement>>());

  const updateStatus = useCallback((updates: Partial<VideoPreloadStatus>) => {
    setStatus(prev => {
      const newStatus = { ...prev, ...updates };
      newStatus.progress = newStatus.total > 0 ? (newStatus.loaded / newStatus.total) * 100 : 0;
      newStatus.isComplete = newStatus.loaded >= newStatus.total;
      return newStatus;
    });
  }, []);

  const preloadSingleVideo = useCallback(
    (
      url: string, 
      options: VideoPreloadOptions = {}
    ): Promise<HTMLVideoElement> => {
      // Return cached video if available
      if (videoCache.has(url)) {
        return Promise.resolve(videoCache.get(url)!);
      }

      // Return existing promise if already loading
      if (loadingPromises.has(url)) {
        return loadingPromises.get(url)!;
      }

      const {
        priority = 'normal',
        retryAttempts = 2,
        retryDelay = 1000,
        onError
      } = options;

      const attemptLoad = (attempt: number): Promise<HTMLVideoElement> => {
        return new Promise((resolve, reject) => {
          const video = document.createElement('video');
          video.src = url;
          video.preload = priority === 'high' ? 'auto' : 'metadata';
          video.muted = true;
          video.playsInline = true;

          let resolved = false;

          const cleanup = () => {
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('canplaythrough', onCanPlayThrough);
            video.removeEventListener('error', onVideoError);
          };

          const resolveVideo = () => {
            if (resolved) return;
            resolved = true;
            cleanup();
            videoCache.set(url, video);
            loadingPromises.delete(url);
            resolve(video);
          };

          const onLoadedMetadata = () => {
            if (priority === 'normal') {
              resolveVideo();
            }
          };

          const onCanPlayThrough = () => {
            resolveVideo();
          };

          const onVideoError = async (e: Event) => {
            if (resolved) return;
            resolved = true;
            cleanup();
            
            const errorMsg = `Failed to load video: ${url} (attempt ${attempt}/${retryAttempts + 1})`;
            console.error(errorMsg, e);

            if (attempt < retryAttempts) {
              // Retry after delay
              setTimeout(() => {
                attemptLoad(attempt + 1).then(resolve).catch(reject);
              }, retryDelay);
            } else {
              // Final failure
              loadingPromises.delete(url);
              if (onError) onError(errorMsg);
              reject(new Error(errorMsg));
            }
          };

          video.addEventListener('loadedmetadata', onLoadedMetadata);
          video.addEventListener('canplaythrough', onCanPlayThrough);
          video.addEventListener('error', onVideoError);

          // Set a timeout for very slow connections
          setTimeout(() => {
            if (!resolved && priority === 'high') {
              onVideoError(new Event('timeout'));
            }
          }, 30000); // 30 second timeout for high priority videos
        });
      };

      const promise = attemptLoad(0);
      loadingPromises.set(url, promise);
      return promise;
    },
    [videoCache, loadingPromises]
  );

  const preloadVideos = useCallback(
    async (urls: string[], options: VideoPreloadOptions = {}) => {
      const { onProgress, onComplete, onError } = options;
      
      // Reset status
      updateStatus({
        loaded: 0,
        total: urls.length,
        isComplete: false,
        errors: [],
        progress: 0,
      });

      let loadedCount = 0;
      const errors: string[] = [];

      // Create promises for all videos
      const promises = urls.map(async (url) => {
        try {
          await preloadSingleVideo(url, options);
          loadedCount++;
          
          const newStatus = {
            loaded: loadedCount,
            total: urls.length,
            errors: [...errors],
            isComplete: loadedCount >= urls.length,
            progress: (loadedCount / urls.length) * 100,
          };
          
          updateStatus(newStatus);
          if (onProgress) onProgress(newStatus);
          
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error);
          errors.push(errorMsg);
          loadedCount++;
          
          const newStatus = {
            loaded: loadedCount,
            total: urls.length,
            errors: [...errors],
            isComplete: loadedCount >= urls.length,
            progress: (loadedCount / urls.length) * 100,
          };
          
          updateStatus(newStatus);
          if (onProgress) onProgress(newStatus);
          if (onError) onError(errorMsg);
        }
      });

      // Wait for all videos to complete (success or failure)
      await Promise.allSettled(promises);
      
      if (onComplete) onComplete();
    },
    [preloadSingleVideo, updateStatus]
  );

  const isVideoReady = useCallback((url: string): boolean => {
    return videoCache.has(url);
  }, [videoCache]);

  const getPreloadedVideo = useCallback((url: string): HTMLVideoElement | null => {
    return videoCache.get(url) || null;
  }, [videoCache]);

  const clearCache = useCallback(() => {
    // Clean up video elements
    videoCache.forEach(video => {
      video.src = '';
      video.load();
    });
    videoCache.clear();
    loadingPromises.clear();
    
    updateStatus({
      loaded: 0,
      total: 0,
      isComplete: false,
      errors: [],
      progress: 0,
    });
  }, [videoCache, loadingPromises, updateStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearCache();
    };
  }, [clearCache]);

  return {
    status,
    preloadVideos,
    isVideoReady,
    getPreloadedVideo,
    clearCache,
  };
}
