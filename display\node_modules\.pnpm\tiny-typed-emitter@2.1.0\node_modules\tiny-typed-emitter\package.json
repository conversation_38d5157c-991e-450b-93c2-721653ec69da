{"name": "tiny-typed-emitter", "version": "2.1.0", "description": "Fully type-checked EventEmitter", "main": "lib/index", "repository": "https://github.com/binier/tiny-typed-emitter.git", "author": "<PERSON><PERSON><PERSON> <zura.benash<PERSON>@gmail.com>", "license": "MIT", "keywords": ["tiny-typed-emitter", "typed-event-emitter", "typescript", "typescript-library", "typescript-boilerplate", "nodejs", "nodejs-library", "event", "events", "event-emitter", "event-emitters", "eventemitter", "eventemitter3", "definitelytyped", "typings", "definitely-typed"], "files": ["lib/*"], "scripts": {"release": "npm publish"}, "devDependencies": {"@types/node": "^13.13.4", "typescript": "^3.8.3"}}