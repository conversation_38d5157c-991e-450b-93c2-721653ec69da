Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8A79F0000 ntdll.dll
7FF8A72B0000 KERNEL32.DLL
7FF8A5090000 KERNELBASE.dll
7FF8A5A20000 USER32.dll
7FF8A5440000 win32u.dll
7FF8A7770000 GDI32.dll
7FF8A5750000 gdi32full.dll
7FF8A5660000 msvcp_win.dll
7FF8A5870000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8A6FC0000 advapi32.dll
7FF8A7070000 msvcrt.dll
7FF8A7210000 sechost.dll
7FF8A6400000 RPCRT4.dll
7FF8A5390000 bcrypt.dll
7FF8A4950000 CRYPTBASE.DLL
7FF8A55D0000 bcryptPrimitives.dll
7FF8A7180000 IMM32.DLL
