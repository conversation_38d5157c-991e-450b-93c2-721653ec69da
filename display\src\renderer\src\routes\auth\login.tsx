import { useState } from "react";
import {
  createFileRoute,
  useNavigate,
  useRouter,
} from "@tanstack/react-router";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { toast } from "sonner";

import { useAuthActions } from "@renderer/stores/auth.store";
import { useMutation } from "@tanstack/react-query";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import authService, { loginSchema } from "@renderer/services/auth.service";

import { Input } from "@renderer/components/ui/input";
import { Button } from "@renderer/components/ui/button";
import { ButtonLoading } from "@renderer/components/loading/ButtonLoading";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@renderer/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@renderer/components/ui/form";

export const Route = createFileRoute("/auth/login")({
  head: () => ({
    meta: [
      {
        title: "Login",
      },
    ],
  }),
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();
  const router = useRouter();

  const { authenticate } = useAuthActions();

  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const loginMutation = useMutation({
    mutationFn: authService.login,
    onSuccess: (data) => {
      authenticate(data.user, data.access_token);
      router.invalidate().then(() => navigate({ to: "/", replace: true }));
    },
    onError: (err) => toast.error(err.response.data.message),
  });

  const onSubmit = (values: z.infer<typeof loginSchema>) =>
    loginMutation.mutate(values);

  return (
    <div className="flex size-fit flex-col items-center gap-4 rounded-[20px] bg-white p-6 text-center shadow-[0_0_20px_rgba(55,179,74,0.6)]">
      <p className="text-2xl text-[#37b34a]">Cashier Login</p>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col items-center gap-4"
        >
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    {...field}
                    type="text"
                    placeholder="Username"
                    className="border-[37b34a]/50"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    {...field}
                    type="password"
                    placeholder="Password"
                    className="border-[37b34a]/50"
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <Button
            type="submit"
            disabled={loginMutation.isPending}
            className="w-fit bg-red-700"
            style={{
              backgroundColor: "#37b34a",
            }}
          >
            {loginMutation.isPending ? <ButtonLoading /> : "Enter"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
