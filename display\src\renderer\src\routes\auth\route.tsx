import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/auth")({
  beforeLoad({ context: { auth } }) {
    if (auth.isAuthenticated && auth.user) {
      throw redirect({ to: "/", replace: true });
    }
  },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <main
      className="flex h-full w-[100%] justify-center pt-12.5"
      style={{
        background:
          "radial-gradient(ellipse at center,rgba(55,179,74,.3) 0%,rgba(255,255,255,0) 65%,rgba(255,255,255,0) 100%)",
      }}
    >
      <Outlet />
    </main>
  );
}
